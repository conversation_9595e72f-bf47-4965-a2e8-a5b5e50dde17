'use client';

import { useEffect, useRef, useCallback } from 'react';

export interface UseRealTimeUpdatesProps {
  activeRunIds: string[];
  onUpdate: (jobId: string) => void;
  interval?: number;
  enabled?: boolean;
}

export function useRealTimeUpdates({
  activeRunIds,
  onUpdate,
  interval = 5000, // 5 seconds
  enabled = true
}: UseRealTimeUpdatesProps) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isPollingRef = useRef(false);

  const pollActiveRuns = useCallback(async () => {
    if (!enabled || activeRunIds.length === 0 || isPollingRef.current) {
      return;
    }

    isPollingRef.current = true;

    try {
      // Poll each active run for updates
      const updatePromises = activeRunIds.map(async (jobId) => {
        try {
          await onUpdate(jobId);
        } catch (error) {
          console.error(`Failed to update job ${jobId}:`, error);
        }
      });

      await Promise.all(updatePromises);
    } catch (error) {
      console.error('Failed to poll active runs:', error);
    } finally {
      isPollingRef.current = false;
    }
  }, [activeRunIds, onUpdate, enabled]);

  // Set up polling interval
  useEffect(() => {
    if (!enabled || activeRunIds.length === 0) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Start polling
    intervalRef.current = setInterval(pollActiveRuns, interval);

    // Cleanup on unmount or dependency change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [activeRunIds, interval, enabled, pollActiveRuns]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Manual trigger for immediate update
  const triggerUpdate = useCallback(() => {
    pollActiveRuns();
  }, [pollActiveRuns]);

  return {
    triggerUpdate,
    isPolling: isPollingRef.current
  };
}
