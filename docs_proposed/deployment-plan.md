# 🚀 Deployment Plan for Email Analysis Status Page

## Overview

This document outlines the deployment strategy for the Email Analysis Status page, including phased rollout, database migrations, monitoring, and rollback procedures.

## Deployment Strategy

### Phase 1: Backend Infrastructure (Week 1)
**Objective**: Deploy backend changes with backward compatibility

#### Database Schema Updates
1. **Add `sourceType` field to `analysisJobs` collection**
   ```bash
   # Deploy Firestore indexes
   firebase deploy --only firestore:indexes --project ddjs-dev-458016
   ```

2. **Update existing jobs with default sourceType**
   ```typescript
   // Migration script (run once)
   npm run migrate:add-source-type
   ```

#### API Endpoint Deployment
1. **Deploy new `/api/analysis-jobs` endpoint**
   ```bash
   # Deploy to dev environment
   ./scripts/deploy-complete.sh
   ```

2. **Enhanced `/api/jobs/[jobId]/status` endpoint**
   - Backward compatible changes
   - Additional fields in response

#### Verification Steps
- [ ] New API endpoints respond correctly
- [ ] Database queries perform within SLA
- [ ] Existing functionality unaffected
- [ ] Migration script completes successfully

### Phase 2: Frontend Components (Week 2)
**Objective**: Deploy new UI components and page structure

#### Component Development
1. **Create new `/analysis` page**
2. **Develop reusable table components**
3. **Migrate `EmailDateSelector` component**

#### Navigation Updates
1. **Add "Analysis" to navigation menu**
2. **Update routing configuration**

#### Deployment Steps
```bash
# Build and test
npm run build
npm run test:unit
npm run test:integration

# Deploy to dev environment
./scripts/deploy-complete.sh

# Verify deployment
npm run test:e2e -- tests/e2e/analysis-page.spec.ts
```

#### Verification Steps
- [ ] New page loads correctly
- [ ] Table displays data properly
- [ ] Filters and sorting work
- [ ] Mobile responsiveness verified
- [ ] Real-time updates functional

### Phase 3: Migration & Cleanup (Week 3)
**Objective**: Complete migration and remove old functionality

#### Dashboard Updates
1. **Remove `EmailDateSelector` from dashboard**
2. **Add link to new Analysis page**
3. **Update user guidance**

#### Final Testing
1. **End-to-end workflow testing**
2. **Performance testing**
3. **User acceptance testing**

## Database Migration Plan

### Migration Script: Add Source Type

```typescript
// scripts/migrate-add-source-type.ts
import { Database } from '@webapp/services';

async function migrateSourceType() {
  const database = new Database();
  const batchSize = 100;
  let processed = 0;
  
  console.log('Starting sourceType migration...');
  
  try {
    const jobsRef = database.collection('analysisJobs');
    let query = jobsRef.limit(batchSize);
    
    while (true) {
      const snapshot = await query.get();
      
      if (snapshot.empty) {
        break;
      }
      
      const batch = database.batch();
      let batchCount = 0;
      
      for (const doc of snapshot.docs) {
        const data = doc.data();
        
        // Only update if sourceType doesn't exist
        if (!data.sourceType) {
          batch.update(doc.ref, {
            sourceType: 'manual', // Default for existing jobs
            updatedAt: new Date()
          });
          batchCount++;
        }
      }
      
      if (batchCount > 0) {
        await batch.commit();
        processed += batchCount;
        console.log(`Migrated ${processed} jobs...`);
      }
      
      // Get next batch
      const lastDoc = snapshot.docs[snapshot.docs.length - 1];
      query = jobsRef.startAfter(lastDoc).limit(batchSize);
    }
    
    console.log(`Migration completed. Total jobs migrated: ${processed}`);
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Run migration
migrateSourceType()
  .then(() => process.exit(0))
  .catch(() => process.exit(1));
```

### Migration Execution

```bash
# Development environment
npm run migrate:add-source-type

# Production environment (after dev testing)
NODE_ENV=production npm run migrate:add-source-type
```

## Monitoring & Observability

### Key Metrics to Monitor

#### Performance Metrics
- **Page Load Time**: < 2 seconds
- **API Response Time**: < 500ms
- **Database Query Time**: < 200ms
- **Table Render Time**: < 300ms

#### Business Metrics
- **User Adoption**: % of users visiting new page
- **Feature Usage**: Analysis starts from new page
- **Error Rates**: < 1% for critical paths
- **User Satisfaction**: Feedback scores

### Monitoring Setup

```typescript
// Monitoring configuration
const monitoringConfig = {
  alerts: [
    {
      name: 'Analysis Page Load Time',
      condition: 'page_load_time > 2000ms',
      severity: 'warning',
      channels: ['slack', 'email']
    },
    {
      name: 'Analysis Jobs API Error Rate',
      condition: 'error_rate > 5%',
      severity: 'critical',
      channels: ['slack', 'pagerduty']
    },
    {
      name: 'Database Migration Status',
      condition: 'migration_failed',
      severity: 'critical',
      channels: ['slack', 'email']
    }
  ],
  dashboards: [
    {
      name: 'Analysis Page Performance',
      metrics: [
        'page_load_time',
        'api_response_time',
        'user_engagement',
        'error_rates'
      ]
    }
  ]
};
```

### Health Checks

```typescript
// Health check endpoint for new functionality
// apps/webapp/src/app/api/health/analysis/route.ts
export async function GET() {
  try {
    // Test database connectivity
    const database = new Database();
    await database.collection('analysisJobs').limit(1).get();
    
    // Test API endpoints
    const testResponse = await fetch('/api/analysis-jobs?limit=1');
    if (!testResponse.ok) {
      throw new Error('Analysis jobs API unhealthy');
    }
    
    return Response.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: 'ok',
        api: 'ok'
      }
    });
    
  } catch (error) {
    return Response.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    }, { status: 503 });
  }
}
```

## Rollback Procedures

### Immediate Rollback (< 5 minutes)

```bash
# Rollback to previous deployment
gcloud run deploy webapp \
  --image=gcr.io/ddjs-dev-458016/webapp:previous-stable \
  --region=us-central1 \
  --project=ddjs-dev-458016

# Verify rollback
curl -f https://dev.datadrivenjobsearch.com/api/health
```

### Database Rollback (if needed)

```typescript
// Rollback script for sourceType field
async function rollbackSourceType() {
  const database = new Database();
  const batchSize = 100;
  
  console.log('Rolling back sourceType field...');
  
  const jobsRef = database.collection('analysisJobs');
  let query = jobsRef.where('sourceType', '!=', null).limit(batchSize);
  
  while (true) {
    const snapshot = await query.get();
    
    if (snapshot.empty) {
      break;
    }
    
    const batch = database.batch();
    
    for (const doc of snapshot.docs) {
      batch.update(doc.ref, {
        sourceType: FieldValue.delete()
      });
    }
    
    await batch.commit();
    console.log(`Rolled back ${snapshot.size} jobs...`);
    
    // Continue with next batch
    const lastDoc = snapshot.docs[snapshot.docs.length - 1];
    query = jobsRef.where('sourceType', '!=', null).startAfter(lastDoc).limit(batchSize);
  }
  
  console.log('Rollback completed');
}
```

### Rollback Decision Matrix

| Issue Severity | Response Time | Action |
|----------------|---------------|---------|
| Critical (site down) | Immediate | Full rollback |
| High (major feature broken) | < 15 minutes | Feature rollback |
| Medium (minor issues) | < 1 hour | Hot fix |
| Low (cosmetic issues) | Next release | Bug fix |

## Testing in Production

### Canary Deployment

```yaml
# Canary deployment configuration
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: webapp
  annotations:
    run.googleapis.com/traffic: |
      {
        "canary": 10,
        "stable": 90
      }
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
    spec:
      containers:
      - image: gcr.io/ddjs-dev-458016/webapp:canary
        env:
        - name: FEATURE_ANALYSIS_PAGE
          value: "true"
```

### Feature Flags

```typescript
// Feature flag configuration
const featureFlags = {
  ANALYSIS_PAGE_ENABLED: {
    development: true,
    staging: true,
    production: false, // Initially disabled
    canary: true
  },
  ANALYSIS_PAGE_MIGRATION: {
    development: true,
    staging: true,
    production: false,
    canary: true
  }
};

// Usage in components
function Navigation() {
  const analysisPageEnabled = useFeatureFlag('ANALYSIS_PAGE_ENABLED');
  
  return (
    <nav>
      {/* ... other nav items */}
      {analysisPageEnabled && (
        <Link href="/analysis">Analysis</Link>
      )}
    </nav>
  );
}
```

## Post-Deployment Verification

### Automated Tests

```bash
# Run post-deployment tests
npm run test:e2e:production

# Performance tests
npm run test:performance

# Smoke tests
npm run test:smoke
```

### Manual Verification Checklist

#### Functionality Tests
- [ ] Analysis page loads correctly
- [ ] Table displays user's jobs
- [ ] Filters work properly
- [ ] Sorting functions correctly
- [ ] Pagination works
- [ ] Real-time updates active
- [ ] Mobile layout responsive
- [ ] Start analysis functionality works
- [ ] Navigation updated

#### Performance Tests
- [ ] Page load time < 2 seconds
- [ ] Table renders < 500ms
- [ ] API responses < 300ms
- [ ] No memory leaks detected
- [ ] Mobile performance acceptable

#### Integration Tests
- [ ] Authentication works
- [ ] Database queries efficient
- [ ] Real-time polling active
- [ ] Error handling graceful
- [ ] Logging functioning

## Communication Plan

### Stakeholder Updates

#### Pre-Deployment
- **Development Team**: Technical implementation details
- **Product Team**: Feature capabilities and timeline
- **Support Team**: New functionality training
- **Users**: Advance notice of new features

#### During Deployment
- **Real-time Updates**: Slack channel for deployment status
- **Issue Escalation**: Clear escalation path for problems
- **Rollback Authority**: Designated decision makers

#### Post-Deployment
- **Success Metrics**: Performance and adoption reports
- **Issue Resolution**: Bug reports and fixes
- **User Feedback**: Collection and analysis
- **Lessons Learned**: Retrospective documentation

### User Communication

```markdown
# Email Analysis Status Page - Now Available!

We're excited to announce the new Email Analysis Status page, providing you with comprehensive visibility into all your email analysis jobs.

## What's New
- **Centralized Management**: View all your analysis jobs in one place
- **Real-time Updates**: Live progress tracking for running analyses
- **Enhanced Filtering**: Filter by source type, status, and date range
- **Mobile Optimized**: Full functionality on mobile devices

## How to Access
Navigate to the new "Analysis" tab in the main menu, or visit [/analysis](/analysis) directly.

## Migration Notice
The "Start Historical Email Analysis" feature has moved from the Dashboard to the new Analysis page for better organization.

## Need Help?
Contact <NAME_EMAIL> or check our updated documentation.
```

---

*This deployment plan ensures a smooth, monitored rollout of the Email Analysis Status page with minimal risk and maximum user value.*
