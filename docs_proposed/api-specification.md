# 🔌 API Specification for Email Analysis Status Page

## Overview

This document specifies the new API endpoints required for the Email Analysis Status page, including request/response schemas, error handling, and integration patterns.

## New API Endpoints

### GET /api/analysis-jobs

Retrieves a paginated list of analysis jobs for the authenticated user.

#### Request

```http
GET /api/analysis-jobs?page=1&limit=20&sourceType=manual&status=completed
Authorization: Bearer <token>
```

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | number | No | 1 | Page number (1-based) |
| `limit` | number | No | 20 | Items per page (max 100) |
| `sourceType` | string | No | - | Filter by 'manual' or 'automatic' |
| `status` | string | No | - | Filter by job status |
| `startDate` | string | No | - | Filter jobs created after date (YYYY-MM-DD) |
| `endDate` | string | No | - | Filter jobs created before date (YYYY-MM-DD) |
| `sortBy` | string | No | 'createdAt' | Sort field ('createdAt', 'status', 'totalEmails') |
| `sortOrder` | string | No | 'desc' | Sort order ('asc', 'desc') |

#### Response Schema

```typescript
interface ListAnalysisJobsResponse {
  jobs: AnalysisJobSummary[];
  pagination: PaginationInfo;
  filters: AppliedFilters;
}

interface AnalysisJobSummary {
  jobId: string;
  sourceType: 'manual' | 'automatic';
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  
  // Email counts
  totalEmails: number;
  cachedEmails: number;
  pendingEmails: number;        // calculated: totalEmails - processedEmails
  successfullyAnalyzed: number; // same as processedEmails
  
  // Date information
  dateRange: {
    start: string;  // YYYY-MM-DD format
    end: string;    // YYYY-MM-DD format
  };
  jobStartTime: string;    // ISO timestamp
  lastUpdated: string;     // ISO timestamp
  completedAt?: string;    // ISO timestamp
  
  // Status information
  currentStatus: string;   // Human-readable status message
  error?: string;          // Error message if status is 'error'
  
  // Progress information (for active jobs)
  progress?: {
    percentage: number;    // 0-100
    estimatedTimeRemaining?: number; // seconds
  };
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalJobs: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  pageSize: number;
}

interface AppliedFilters {
  sourceType?: 'manual' | 'automatic';
  status?: string;
  startDate?: string;
  endDate?: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}
```

#### Success Response

```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "jobs": [
    {
      "jobId": "550e8400-e29b-41d4-a716-446655440000",
      "sourceType": "manual",
      "status": "completed",
      "totalEmails": 150,
      "cachedEmails": 45,
      "pendingEmails": 0,
      "successfullyAnalyzed": 150,
      "dateRange": {
        "start": "2024-01-01",
        "end": "2024-01-31"
      },
      "jobStartTime": "2024-01-15T10:30:00Z",
      "lastUpdated": "2024-01-15T11:45:00Z",
      "completedAt": "2024-01-15T11:45:00Z",
      "currentStatus": "Analysis completed successfully"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalJobs": 87,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "pageSize": 20
  },
  "filters": {
    "sourceType": "manual",
    "sortBy": "createdAt",
    "sortOrder": "desc"
  }
}
```

#### Error Responses

```http
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "error": "Invalid query parameters",
  "details": {
    "limit": "Must be between 1 and 100",
    "sortBy": "Must be one of: createdAt, status, totalEmails"
  }
}
```

```http
HTTP/1.1 401 Unauthorized
Content-Type: application/json

{
  "error": "Authentication required",
  "message": "Please provide a valid authorization token"
}
```

## Enhanced Existing Endpoints

### GET /api/jobs/[jobId]/status

Enhanced to include additional fields for the status page.

#### Enhanced Response Schema

```typescript
interface EnhancedJobStatusResponse {
  jobId: string;
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  sourceType: 'manual' | 'automatic';  // NEW FIELD
  
  progress: {
    total: number;
    processed: number;
    current: string;
    cachedCount: number;
    newAnalysisCount: number;
    remainingTokens?: number;
    error?: string;
    percentage: number;  // NEW: calculated field
  };
  
  // Enhanced timing information
  timing: {  // NEW SECTION
    createdAt: string;
    updatedAt: string;
    completedAt?: string;
    duration?: number;  // seconds
    estimatedTimeRemaining?: number;  // seconds for active jobs
  };
  
  // Email analysis details
  emailDetails: {  // NEW SECTION
    dateRange: {
      start: string;
      end: string;
    };
    monitoredEmail: string;
    totalEmails: number;
    pendingEmails: number;
  };
  
  result?: any;  // Existing field
}
```

## Implementation Details

### Database Query Optimization

```typescript
// Efficient pagination query
async function getAnalysisJobs(userId: string, options: QueryOptions) {
  let query = database
    .collection('analysisJobs')
    .where('clerkUserId', '==', userId);
  
  // Apply filters
  if (options.sourceType) {
    query = query.where('sourceType', '==', options.sourceType);
  }
  
  if (options.status) {
    query = query.where('status', '==', options.status);
  }
  
  // Apply sorting
  query = query.orderBy(options.sortBy || 'createdAt', options.sortOrder || 'desc');
  
  // Apply pagination
  if (options.cursor) {
    query = query.startAfter(options.cursor);
  }
  
  query = query.limit(options.limit || 20);
  
  return await query.get();
}
```

### Real-time Updates Integration

```typescript
// WebSocket/SSE integration for live updates
interface JobUpdateMessage {
  type: 'job_status_update';
  jobId: string;
  status: string;
  progress: {
    processed: number;
    total: number;
    percentage: number;
  };
  timestamp: string;
}

// Client-side polling fallback
async function pollJobUpdates(jobIds: string[]) {
  const updates = await Promise.all(
    jobIds.map(id => fetch(`/api/jobs/${id}/status`))
  );
  
  return updates.map(response => response.json());
}
```

### Error Handling Strategy

```typescript
// Standardized error responses
interface APIError {
  error: string;
  message: string;
  code?: string;
  details?: Record<string, any>;
  timestamp: string;
  requestId: string;
}

// Error handling middleware
function handleAPIError(error: Error, req: Request): APIError {
  const requestId = req.headers['x-request-id'] || generateRequestId();
  
  if (error instanceof ValidationError) {
    return {
      error: 'Validation Error',
      message: error.message,
      code: 'VALIDATION_FAILED',
      details: error.details,
      timestamp: new Date().toISOString(),
      requestId
    };
  }
  
  // Handle other error types...
  return {
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    timestamp: new Date().toISOString(),
    requestId
  };
}
```

## Rate Limiting

### API Rate Limits

| Endpoint | Rate Limit | Window |
|----------|------------|--------|
| `GET /api/analysis-jobs` | 60 requests | 1 minute |
| `GET /api/jobs/[id]/status` | 120 requests | 1 minute |
| `POST /api/emails/analyze` | 10 requests | 1 minute |

### Implementation

```typescript
// Rate limiting middleware
const rateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // limit each IP to 60 requests per windowMs
  message: {
    error: 'Too Many Requests',
    message: 'Rate limit exceeded. Please try again later.',
    retryAfter: 60
  }
});
```

## Caching Strategy

### Response Caching

```typescript
// Cache configuration
const cacheConfig = {
  '/api/analysis-jobs': {
    ttl: 30, // 30 seconds
    vary: ['user-id', 'query-params']
  },
  '/api/jobs/[id]/status': {
    ttl: 5, // 5 seconds for active jobs
    ttl_completed: 300, // 5 minutes for completed jobs
    vary: ['user-id', 'job-id']
  }
};

// Cache key generation
function generateCacheKey(endpoint: string, userId: string, params: any): string {
  const paramString = JSON.stringify(params, Object.keys(params).sort());
  return `${endpoint}:${userId}:${btoa(paramString)}`;
}
```

## Testing Strategy

### API Test Cases

```typescript
describe('GET /api/analysis-jobs', () => {
  test('should return paginated jobs for authenticated user', async () => {
    const response = await request(app)
      .get('/api/analysis-jobs?page=1&limit=10')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200);
    
    expect(response.body).toHaveProperty('jobs');
    expect(response.body).toHaveProperty('pagination');
    expect(response.body.jobs).toHaveLength(10);
  });
  
  test('should filter by source type', async () => {
    const response = await request(app)
      .get('/api/analysis-jobs?sourceType=manual')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200);
    
    response.body.jobs.forEach(job => {
      expect(job.sourceType).toBe('manual');
    });
  });
  
  test('should handle invalid pagination parameters', async () => {
    await request(app)
      .get('/api/analysis-jobs?page=0&limit=101')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(400);
  });
});
```

---

*This API specification provides the foundation for the Email Analysis Status page backend functionality.*
