'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { useAuth } from '@clerk/nextjs';

// Types
export interface AnalysisRunSummary {
  jobId: string;
  sourceType: 'manual' | 'automatic';
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  totalEmails: number;
  cachedEmails: number;
  pendingEmails: number;
  successfullyAnalyzed: number;
  emailDateRange: {
    start: string;
    end: string;
  };
  analysisStartTime: string;
  lastUpdated: string;
  completedAt?: string;
  currentStatus: string;
  error?: string;
  progress?: {
    percentage: number;
  };
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalRuns: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  pageSize: number;
}

export interface FilterState {
  sourceType?: 'manual' | 'automatic';
  status?: 'processing' | 'completed' | 'error' | 'cancelled';
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export interface AnalysisRunsState {
  analysisRuns: AnalysisRunSummary[];
  loading: boolean;
  error: string | null;
  pagination: PaginationInfo;
  filters: FilterState;
  lastRefresh: number;
}

const initialState: AnalysisRunsState = {
  analysisRuns: [],
  loading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalRuns: 0,
    hasNextPage: false,
    hasPreviousPage: false,
    pageSize: 20
  },
  filters: {
    sortBy: 'analysisStartTime',
    sortOrder: 'desc'
  },
  lastRefresh: 0
};

export function useAnalysisRuns() {
  const [state, setState] = useState<AnalysisRunsState>(initialState);
  const { getToken } = useAuth();
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchAnalysisRuns = useCallback(async (options?: Partial<FilterState & { page?: number }>) => {
    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const token = await getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      // Build query parameters
      const params = new URLSearchParams();
      const currentFilters = { ...state.filters, ...options };
      const page = options?.page || state.pagination.currentPage;

      params.set('page', page.toString());
      params.set('limit', state.pagination.pageSize.toString());
      params.set('sortBy', currentFilters.sortBy);
      params.set('sortOrder', currentFilters.sortOrder);

      if (currentFilters.sourceType) {
        params.set('sourceType', currentFilters.sourceType);
      }
      if (currentFilters.status) {
        params.set('status', currentFilters.status);
      }

      const response = await fetch(`/api/analysis-runs?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        signal // Add abort signal
      });

      // Check if request was aborted
      if (signal.aborted) {
        return;
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Check again if request was aborted before updating state
      if (signal.aborted) {
        return;
      }

      setState(prev => ({
        ...prev,
        analysisRuns: data.analysisRuns || [],
        pagination: data.pagination || prev.pagination,
        filters: { ...prev.filters, ...currentFilters },
        loading: false,
        lastRefresh: Date.now()
      }));

    } catch (error) {
      // Don't update state if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error('Failed to fetch analysis runs:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch analysis runs'
      }));
    }
  }, [getToken, state.filters, state.pagination.currentPage, state.pagination.pageSize]);

  const refreshAnalysisRun = useCallback(async (jobId: string) => {
    try {
      const token = await getToken();
      if (!token) return;

      const response = await fetch(`/api/jobs/${jobId}/status`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) return;

      const jobData = await response.json();

      // Update the specific job in the list
      setState(prev => ({
        ...prev,
        analysisRuns: prev.analysisRuns.map(run => 
          run.jobId === jobId 
            ? {
                ...run,
                status: jobData.status,
                successfullyAnalyzed: jobData.progress?.processed || run.successfullyAnalyzed,
                pendingEmails: jobData.progress?.pendingEmails || run.pendingEmails,
                currentStatus: jobData.progress?.current || run.currentStatus,
                lastUpdated: jobData.timing?.updatedAt || run.lastUpdated,
                completedAt: jobData.timing?.completedAt || run.completedAt,
                error: jobData.progress?.error || run.error,
                progress: jobData.progress?.percentage ? { percentage: jobData.progress.percentage } : run.progress
              }
            : run
        ),
        lastRefresh: Date.now()
      }));

    } catch (error) {
      console.error('Failed to refresh analysis run:', error);
    }
  }, [getToken]);

  const updateFilters = useCallback((newFilters: Partial<FilterState>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...newFilters },
      pagination: { ...prev.pagination, currentPage: 1 } // Reset to first page when filtering
    }));
  }, []);

  const changePage = useCallback((page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, currentPage: page }
    }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const refresh = useCallback(() => {
    fetchAnalysisRuns();
  }, [fetchAnalysisRuns]);

  // Initial load
  useEffect(() => {
    fetchAnalysisRuns();
  }, []);

  // Refetch when filters or page changes
  useEffect(() => {
    if (state.lastRefresh > 0) { // Skip initial load
      fetchAnalysisRuns();
    }
  }, [state.filters, state.pagination.currentPage]);

  return {
    ...state,
    actions: {
      fetchAnalysisRuns,
      refreshAnalysisRun,
      updateFilters,
      changePage,
      clearError,
      refresh
    }
  };
}
