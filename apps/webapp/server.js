const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const path = require('path');

// Socket.IO has been removed from the application
// Real-time communication is now handled via polling-based architecture

// Start the server
async function startServer() {
  const dev = process.env.NODE_ENV !== 'production';
  const hostname = 'localhost';
  const port = process.env.PORT || 3000;

  // Initialize Next.js
  const app = next({ dev, hostname, port });
  const handle = app.getRequestHandler();

  try {
    await app.prepare();

    // Create HTTP server
    const server = createServer((req, res) => {
      const parsedUrl = parse(req.url, true);
      handle(req, res, parsedUrl);
    });

    // Start listening
    server.listen(port, (err) => {
      if (err) throw err;
      console.log(`> Ready on http://${hostname}:${port} (Socket.IO removed - using polling-based architecture)`);
    });
  } catch (error) {
    console.error('Error starting server:', error);
    process.exit(1);
  }
}

startServer();
