'use client';

import React, { useMemo } from 'react';
import { ArrowPathIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useAnalysisRuns } from './hooks/useAnalysisRuns';
import { useRealTimeUpdates } from './hooks/useRealTimeUpdates';
import { AnalysisRunsTable } from './components/AnalysisRunsTable';
import { AnalysisFilters } from './components/AnalysisFilters';
import { StartAnalysisSection } from './components/StartAnalysisSection';

export default function AnalysisPage() {
  const {
    analysisRuns,
    loading,
    error,
    pagination,
    filters,
    lastRefresh,
    actions
  } = useAnalysisRuns();

  // Get active analysis runs for real-time updates
  const activeRunIds = useMemo(() => {
    return analysisRuns
      .filter(run => run.status === 'processing')
      .map(run => run.jobId);
  }, [analysisRuns]);

  // Set up real-time updates for active runs
  useRealTimeUpdates({
    activeRunIds,
    onUpdate: actions.refreshAnalysisRun,
    enabled: activeRunIds.length > 0
  });

  const handleAnalysisStart = (startDate: string, endDate: string) => {
    console.log('Analysis started:', { startDate, endDate });
    // Refresh the list to show the new analysis run
    setTimeout(() => {
      actions.refresh();
    }, 2000); // Give the backend time to create the job
  };

  const handleAnalysisComplete = (result: any) => {
    console.log('Analysis completed:', result);
    // Refresh the list to update the completed status
    actions.refresh();
  };

  const handleRunSelect = (jobId: string) => {
    console.log('Selected analysis run:', jobId);
    // Future: Navigate to detailed view or show modal
  };

  const formatLastRefresh = () => {
    if (!lastRefresh) return '';
    const now = Date.now();
    const diff = now - lastRefresh;
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `Updated ${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `Updated ${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `Updated ${hours}h ago`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Email Analysis Status</h1>
              <p className="mt-2 text-gray-600">
                Monitor and manage your email analysis runs. View progress, results, and start new analyses.
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              {lastRefresh > 0 && (
                <span className="text-sm text-gray-500">
                  {formatLastRefresh()}
                </span>
              )}
              
              <button
                onClick={actions.refresh}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                <ArrowPathIcon className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 border border-red-200 rounded-md bg-red-50">
            <div className="flex items-center justify-between">
              <div className="text-red-700">
                <strong>Error:</strong> {error}
              </div>
              <button
                onClick={actions.clearError}
                className="text-red-500 hover:text-red-700"
              >
                ×
              </button>
            </div>
          </div>
        )}

        <div className="space-y-6">
          {/* Start Analysis Section */}
          <StartAnalysisSection
            onAnalysisStart={handleAnalysisStart}
            onAnalysisComplete={handleAnalysisComplete}
          />

          {/* Filters */}
          <AnalysisFilters
            filters={filters}
            onFilterChange={actions.updateFilters}
          />

          {/* Analysis Runs Table */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">
                  Analysis Runs
                  {pagination.totalRuns > 0 && (
                    <span className="ml-2 text-sm text-gray-500">
                      ({pagination.totalRuns} total)
                    </span>
                  )}
                </h2>
                
                {activeRunIds.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span className="text-sm text-blue-600">
                      {activeRunIds.length} active run{activeRunIds.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="p-6">
              <AnalysisRunsTable
                analysisRuns={analysisRuns}
                loading={loading}
                pagination={pagination}
                filters={filters}
                onFilterChange={actions.updateFilters}
                onPageChange={actions.changePage}
                onSortChange={(sortBy, sortOrder) => {
                  actions.updateFilters({ sortBy, sortOrder });
                }}
                onRunSelect={handleRunSelect}
              />
            </div>
          </div>

          {/* Summary Stats */}
          {analysisRuns.length > 0 && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {analysisRuns.filter(run => run.status === 'completed').length}
                  </div>
                  <div className="text-sm text-gray-500">Completed</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {analysisRuns.filter(run => run.status === 'processing').length}
                  </div>
                  <div className="text-sm text-gray-500">Processing</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {analysisRuns.filter(run => run.status === 'error').length}
                  </div>
                  <div className="text-sm text-gray-500">Failed</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {analysisRuns.reduce((sum, run) => sum + run.totalEmails, 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500">Total Emails</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
