# 🧩 Component Architecture for Email Analysis Status Page

## Overview

This document outlines the frontend component architecture for the Email Analysis Status page, including component hierarchy, state management, and integration patterns with existing systems.

## Component Hierarchy

```
AnalysisPage
├── PageHeader
│   ├── Title
│   └── ActionButtons
├── StartAnalysisSection
│   ├── EmailDateSelector (migrated)
│   └── PollingEmailAnalysisProgress (migrated)
├── AnalysisJobsTable
│   ├── TableHeader
│   │   ├── ColumnHeaders
│   │   └── SortControls
│   ├── TableFilters
│   │   ├── SourceTypeFilter
│   │   ├── StatusFilter
│   │   └── DateRangeFilter
│   ├── TableBody
│   │   ├── JobRow (multiple)
│   │   │   ├── SourceTypeBadge
│   │   │   ├── StatusBadge
│   │   │   ├── ProgressIndicator
│   │   │   └── ActionMenu
│   │   └── LoadingRow (when loading)
│   └── TableFooter
│       ├── Pagination
│       └── ResultsInfo
└── EmptyState (when no jobs)
```

## System Architecture Diagram

```mermaid
graph TB
    subgraph "Email Analysis Status Page"
        AnalysisPage[Analysis Page Container]
        StartSection[Start Analysis Section]
        JobsTable[Analysis Jobs Table]
        Filters[Table Filters]
        Pagination[Pagination Controls]
    end

    subgraph "Migrated Components"
        EmailDateSelector[Email Date Selector]
        PollingProgress[Polling Progress Component]
    end

    subgraph "New UI Components"
        DataTable[Generic Data Table]
        StatusBadge[Status Badge]
        ProgressIndicator[Progress Indicator]
        SourceTypeBadge[Source Type Badge]
    end

    subgraph "API Layer"
        AnalysisJobsAPI[/api/analysis-jobs]
        JobStatusAPI[/api/jobs/[id]/status]
        AnalyzeAPI[/api/emails/analyze]
    end

    subgraph "State Management"
        PageState[Page State]
        RealTimeUpdates[Real-time Updates]
        FilterState[Filter State]
    end

    AnalysisPage --> StartSection
    AnalysisPage --> JobsTable
    AnalysisPage --> PageState

    StartSection --> EmailDateSelector
    StartSection --> PollingProgress
    StartSection --> AnalyzeAPI

    JobsTable --> DataTable
    JobsTable --> Filters
    JobsTable --> Pagination
    JobsTable --> AnalysisJobsAPI

    DataTable --> StatusBadge
    DataTable --> ProgressIndicator
    DataTable --> SourceTypeBadge

    PageState --> RealTimeUpdates
    PageState --> FilterState

    RealTimeUpdates --> JobStatusAPI
```

## Core Components

### 1. AnalysisPage (Main Container)

```typescript
// apps/webapp/src/app/analysis/page.tsx
interface AnalysisPageProps {}

interface AnalysisPageState {
  jobs: AnalysisJobSummary[];
  loading: boolean;
  error: string | null;
  pagination: PaginationInfo;
  filters: FilterState;
  selectedJobs: string[];
}

export default function AnalysisPage() {
  // State management
  // API integration
  // Real-time updates
  // Error handling
}
```

### 2. AnalysisJobsTable (Data Display)

```typescript
// apps/webapp/src/app/analysis/components/AnalysisJobsTable.tsx
interface AnalysisJobsTableProps {
  jobs: AnalysisJobSummary[];
  loading: boolean;
  pagination: PaginationInfo;
  filters: FilterState;
  onFilterChange: (filters: FilterState) => void;
  onPageChange: (page: number) => void;
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  onJobSelect: (jobId: string) => void;
  onJobAction: (jobId: string, action: JobAction) => void;
}

export function AnalysisJobsTable(props: AnalysisJobsTableProps) {
  // Table rendering
  // Sorting logic
  // Selection handling
  // Action dispatching
}
```

### 3. StartAnalysisSection (Analysis Trigger)

```typescript
// apps/webapp/src/app/analysis/components/StartAnalysisSection.tsx
interface StartAnalysisSectionProps {
  onAnalysisStart: (startDate: string, endDate: string) => void;
  onAnalysisComplete: (result: any) => void;
  disabled?: boolean;
}

export function StartAnalysisSection(props: StartAnalysisSectionProps) {
  // Migrated EmailDateSelector
  // Migrated PollingEmailAnalysisProgress
  // Integration with table refresh
}
```

## Reusable UI Components

### 1. DataTable (Generic Table Component)

```typescript
// apps/webapp/src/app/components/ui/DataTable.tsx
interface Column<T> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: PaginationInfo;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSort?: (key: string, order: 'asc' | 'desc') => void;
  onPageChange?: (page: number) => void;
  emptyMessage?: string;
  loadingRows?: number;
}

export function DataTable<T>(props: DataTableProps<T>) {
  // Generic table implementation
  // Sorting controls
  // Loading states
  // Empty states
}
```

### 2. StatusBadge (Status Indicator)

```typescript
// apps/webapp/src/app/components/ui/StatusBadge.tsx
interface StatusBadgeProps {
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  animated?: boolean;
}

export function StatusBadge(props: StatusBadgeProps) {
  // Status-specific styling
  // Icons and animations
  // Accessibility features
}
```

### 3. ProgressIndicator (Progress Display)

```typescript
// apps/webapp/src/app/components/ui/ProgressIndicator.tsx
interface ProgressIndicatorProps {
  current: number;
  total: number;
  status: string;
  showPercentage?: boolean;
  showNumbers?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function ProgressIndicator(props: ProgressIndicatorProps) {
  // Progress bar
  // Percentage calculation
  // Status text
}
```

## State Management

### 1. Page-Level State

```typescript
// State structure for AnalysisPage
interface AnalysisPageState {
  // Data
  jobs: AnalysisJobSummary[];
  activeJobs: string[]; // Jobs currently being polled
  
  // UI State
  loading: boolean;
  error: string | null;
  selectedJobs: string[];
  
  // Pagination
  pagination: {
    currentPage: number;
    totalPages: number;
    totalJobs: number;
    pageSize: number;
  };
  
  // Filters
  filters: {
    sourceType?: 'manual' | 'automatic';
    status?: JobStatus;
    dateRange?: {
      start: string;
      end: string;
    };
    sortBy: string;
    sortOrder: 'asc' | 'desc';
  };
  
  // Real-time updates
  lastRefresh: number;
  autoRefresh: boolean;
}
```

### 2. State Management Hooks

```typescript
// Custom hooks for state management
export function useAnalysisJobs() {
  const [state, setState] = useState<AnalysisPageState>(initialState);
  
  const fetchJobs = useCallback(async (options?: QueryOptions) => {
    // API call logic
  }, []);
  
  const refreshJob = useCallback(async (jobId: string) => {
    // Single job refresh
  }, []);
  
  const updateFilters = useCallback((newFilters: Partial<FilterState>) => {
    // Filter update logic
  }, []);
  
  return {
    ...state,
    actions: {
      fetchJobs,
      refreshJob,
      updateFilters,
      // ... other actions
    }
  };
}

export function useRealTimeUpdates(activeJobIds: string[]) {
  // Polling logic for active jobs
  // WebSocket integration (future)
  // Update notifications
}
```

## Integration Patterns

### 1. API Integration

```typescript
// API service for analysis jobs
class AnalysisJobsService {
  async getJobs(options: QueryOptions): Promise<ListAnalysisJobsResponse> {
    const params = new URLSearchParams();
    
    if (options.page) params.set('page', options.page.toString());
    if (options.limit) params.set('limit', options.limit.toString());
    if (options.sourceType) params.set('sourceType', options.sourceType);
    if (options.status) params.set('status', options.status);
    
    const response = await fetch(`/api/analysis-jobs?${params}`, {
      headers: {
        'Authorization': `Bearer ${await getToken()}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch jobs: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  async getJobStatus(jobId: string): Promise<EnhancedJobStatusResponse> {
    const response = await fetch(`/api/jobs/${jobId}/status`, {
      headers: {
        'Authorization': `Bearer ${await getToken()}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch job status: ${response.statusText}`);
    }
    
    return response.json();
  }
}
```

### 2. Real-time Updates

```typescript
// Polling strategy for real-time updates
export function useJobPolling(jobIds: string[], interval: number = 5000) {
  const [updates, setUpdates] = useState<Map<string, JobUpdate>>(new Map());
  
  useEffect(() => {
    if (jobIds.length === 0) return;
    
    const pollJobs = async () => {
      try {
        const statusPromises = jobIds.map(id => 
          fetch(`/api/jobs/${id}/status`).then(r => r.json())
        );
        
        const statuses = await Promise.all(statusPromises);
        
        const newUpdates = new Map();
        statuses.forEach((status, index) => {
          newUpdates.set(jobIds[index], status);
        });
        
        setUpdates(newUpdates);
      } catch (error) {
        console.error('Failed to poll job statuses:', error);
      }
    };
    
    // Initial poll
    pollJobs();
    
    // Set up interval
    const intervalId = setInterval(pollJobs, interval);
    
    return () => clearInterval(intervalId);
  }, [jobIds, interval]);
  
  return updates;
}
```

### 3. Navigation Integration

```typescript
// Update Navigation component
const navigation = [
  { name: 'Dashboard', href: '/' },
  { name: 'Analysis', href: '/analysis' }, // NEW
  { name: 'Analytics', href: '/analytics' },
  { name: 'Settings', href: '/settings' },
];
```

## Responsive Design

### 1. Mobile-First Table Design

```typescript
// Responsive table component
export function ResponsiveAnalysisTable(props: AnalysisJobsTableProps) {
  const [isMobile] = useMediaQuery('(max-width: 768px)');
  
  if (isMobile) {
    return <MobileJobsList {...props} />;
  }
  
  return <DesktopJobsTable {...props} />;
}

// Mobile card layout
function MobileJobsList({ jobs }: { jobs: AnalysisJobSummary[] }) {
  return (
    <div className="space-y-4">
      {jobs.map(job => (
        <JobCard key={job.jobId} job={job} />
      ))}
    </div>
  );
}
```

### 2. Adaptive Layouts

```css
/* Responsive grid for analysis page */
.analysis-page {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
  .analysis-page {
    grid-template-columns: 1fr 300px; /* Main content + sidebar */
  }
}

/* Responsive table */
.jobs-table {
  overflow-x: auto;
}

@media (max-width: 768px) {
  .jobs-table {
    display: none; /* Hide table on mobile */
  }
  
  .jobs-cards {
    display: block; /* Show cards on mobile */
  }
}
```

## Performance Optimizations

### 1. Virtual Scrolling (Future Enhancement)

```typescript
// For large datasets
import { FixedSizeList as List } from 'react-window';

function VirtualizedJobsList({ jobs }: { jobs: AnalysisJobSummary[] }) {
  const Row = ({ index, style }: { index: number; style: any }) => (
    <div style={style}>
      <JobRow job={jobs[index]} />
    </div>
  );
  
  return (
    <List
      height={600}
      itemCount={jobs.length}
      itemSize={80}
      width="100%"
    >
      {Row}
    </List>
  );
}
```

### 2. Memoization

```typescript
// Memoized components for performance
export const JobRow = memo(({ job }: { job: AnalysisJobSummary }) => {
  // Component implementation
});

export const AnalysisJobsTable = memo(({ jobs, ...props }: AnalysisJobsTableProps) => {
  // Memoize expensive calculations
  const sortedJobs = useMemo(() => {
    return sortJobs(jobs, props.sortBy, props.sortOrder);
  }, [jobs, props.sortBy, props.sortOrder]);
  
  return (
    // Table implementation
  );
});
```

---

*This component architecture provides a scalable, maintainable foundation for the Email Analysis Status page.*
