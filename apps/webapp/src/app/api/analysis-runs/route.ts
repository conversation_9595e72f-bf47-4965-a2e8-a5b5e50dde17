import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Database } from '@webapp/services';

// Types based on corrected data model
interface AnalysisRunSummary {
  jobId: string;
  sourceType: 'manual' | 'automatic';
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  
  // Email counts (based on actual data model from code analysis)
  totalEmails: number;          // total: emails found in Gmail date range
  cachedEmails: number;         // cachedCount: emails with existing analysis
  pendingEmails: number;        // calculated: total - processed
  successfullyAnalyzed: number; // processed: completed emails (new + cached)
  
  // Date information
  emailDateRange: {             // startDate/endDate: email date range analyzed
    start: string;  // YYYY-MM-DD format
    end: string;    // YYYY-MM-DD format
  };
  analysisStartTime: string;    // createdAt: when analysis run started
  lastUpdated: string;          // updatedAt: last progress update
  completedAt?: string;         // completedAt: when analysis finished
  
  // Status information
  currentStatus: string;        // current: human-readable status message
  error?: string;               // error: failure message if any
  
  // Progress information (for active analysis runs)
  progress?: {
    percentage: number;         // calculated: (processed / total) * 100
  };
}

interface ListAnalysisRunsResponse {
  analysisRuns: AnalysisRunSummary[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRuns: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    pageSize: number;
  };
  filters: {
    sourceType?: 'manual' | 'automatic';
    status?: string;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
  };
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const sourceType = searchParams.get('sourceType') as 'manual' | 'automatic' | null;
    const status = searchParams.get('status');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';

    // Validate parameters
    if (page < 1 || limit < 1) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters',
          details: {
            page: 'Must be >= 1',
            limit: 'Must be between 1 and 100'
          }
        },
        { status: 400 }
      );
    }

    if (sourceType && !['manual', 'automatic'].includes(sourceType)) {
      return NextResponse.json(
        { 
          error: 'Invalid sourceType',
          details: { sourceType: 'Must be "manual" or "automatic"' }
        },
        { status: 400 }
      );
    }

    // Initialize database
    const database = new Database();
    
    // Build query
    let query = database
      .collection('analysisJobs')
      .where('clerkUserId', '==', userId);

    // Apply filters
    if (sourceType) {
      query = query.where('sourceType', '==', sourceType);
    }

    if (status) {
      query = query.where('status', '==', status);
    }

    // Apply sorting
    query = query.orderBy(sortBy, sortOrder);

    // Get total count for pagination (simplified approach)
    const totalSnapshot = await query.get();
    const totalRuns = totalSnapshot.size;
    const totalPages = Math.ceil(totalRuns / limit);

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = query.limit(limit).offset(offset);
    const snapshot = await paginatedQuery.get();

    // Transform documents to AnalysisRunSummary
    const analysisRuns: AnalysisRunSummary[] = snapshot.docs.map(doc => {
      const data = doc.data();
      
      // Calculate pending emails and progress
      const pendingEmails = Math.max(0, (data.total || 0) - (data.processed || 0));
      const progressPercentage = data.total > 0 
        ? Math.round(((data.processed || 0) / data.total) * 100)
        : 0;

      return {
        jobId: data.jobId || doc.id,
        sourceType: data.sourceType || 'manual',
        status: data.status,
        
        // Email counts (based on corrected data model)
        totalEmails: data.total || 0,
        cachedEmails: data.cachedCount || 0,
        pendingEmails,
        successfullyAnalyzed: data.processed || 0,
        
        // Date information
        emailDateRange: {
          start: data.startDate || '',
          end: data.endDate || ''
        },
        analysisStartTime: data.createdAt?.toISOString() || '',
        lastUpdated: data.updatedAt?.toISOString() || '',
        completedAt: data.completedAt?.toISOString(),
        
        // Status information
        currentStatus: data.current || '',
        error: data.error,
        
        // Progress information
        progress: data.status === 'processing' ? {
          percentage: progressPercentage
        } : undefined
      };
    });

    // Build response
    const response: ListAnalysisRunsResponse = {
      analysisRuns,
      pagination: {
        currentPage: page,
        totalPages,
        totalRuns,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
        pageSize: limit
      },
      filters: {
        sourceType: sourceType || undefined,
        status: status || undefined,
        sortBy,
        sortOrder
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching analysis runs:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to fetch analysis runs'
      },
      { status: 500 }
    );
  }
}
