# 🗄️ Database Schema Updates for Email Analysis Status Page

## Overview

This document details the database schema modifications required to support the new Email Analysis Status page functionality, including the addition of source type tracking and enhanced querying capabilities.

## Current Schema Analysis

### Existing `analysisJobs` Collection Structure
Based on code analysis, here's the actual data model:

```typescript
interface AnalysisJobDocument {
  jobId: string;               // UUID (document ID)
  clerkUserId: string;
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  current: string;             // Current status message
  total: number;               // Total emails found in Gmail date range
  processed: number;           // Emails completed (both new analysis + cached)
  cachedCount: number;         // Emails with existing analysis (no tokens used)
  newAnalysisCount: number;    // Emails requiring new AI analysis (tokens used)
  remainingTokens: number;     // User's remaining tokens at job start
  startDate: string;           // Email date range start (YYYY-MM-DD)
  endDate: string;             // Email date range end (YYYY-MM-DD)
  monitoredEmail: string;      // User's monitored Gmail address
  createdAt: Date;             // When analysis run was created
  updatedAt: Date;             // Last update timestamp
  completedAt?: Date;          // When analysis run finished
  error?: string;              // Error message if failed
}
```

### Field Definitions (From Code Analysis)
- **`total`**: Set to `emailIds.length` from Gmail API query for date range
- **`processed`**: Updated by Cloud Functions as `batchIndex + 1` for each completed email
- **`cachedCount`**: Pre-calculated count of emails with existing analysis in Firestore
- **`newAnalysisCount`**: Pre-calculated count of emails needing new AI analysis
- **Pending Count**: Calculated as `total - processed` (emails still being processed)

## Required Schema Updates

### 1. Add Source Type Field

**New Field**: `sourceType`
- **Type**: `'manual' | 'automatic'`
- **Purpose**: Distinguish between user-initiated and webhook-triggered analyses
- **Default**: `'manual'` for backward compatibility

```typescript
interface UpdatedAnalysisJobDocument extends AnalysisJobDocument {
  sourceType: 'manual' | 'automatic';  // NEW FIELD
}
```

### 2. Enhanced Status Tracking

**New Field**: `statusHistory` (Optional enhancement)
- **Type**: `Array<{ status: string; timestamp: Date; message?: string }>`
- **Purpose**: Track status changes over time for debugging and analytics

```typescript
interface StatusHistoryEntry {
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  timestamp: Date;
  message?: string;
}

interface EnhancedAnalysisJobDocument extends UpdatedAnalysisJobDocument {
  statusHistory?: StatusHistoryEntry[];  // OPTIONAL ENHANCEMENT
}
```

## Migration Strategy

### Phase 1: Backward Compatible Addition

1. **Add `sourceType` field to new jobs**
   ```typescript
   // In /api/emails/analyze route
   const jobData = {
     // ... existing fields
     sourceType: 'manual' as const,  // NEW
   }
   ```

2. **Update webhook-triggered jobs**
   ```typescript
   // In notification handler
   const jobData = {
     // ... existing fields
     sourceType: 'automatic' as const,  // NEW
   }
   ```

### Phase 2: Historical Data Migration

**Migration Script** (to be run once):
```typescript
async function migrateExistingJobs() {
  const batch = database.batch();
  const jobsRef = database.collection('analysisJobs');
  
  const snapshot = await jobsRef.get();
  
  snapshot.docs.forEach(doc => {
    const data = doc.data();
    if (!data.sourceType) {
      batch.update(doc.ref, { 
        sourceType: 'manual',  // Default for existing jobs
        updatedAt: new Date()
      });
    }
  });
  
  await batch.commit();
  console.log(`Migrated ${snapshot.size} jobs`);
}
```

## Updated Firestore Indexes

### New Composite Indexes Required

```json
{
  "indexes": [
    {
      "collectionGroup": "analysisJobs",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "clerkUserId", "order": "ASCENDING" },
        { "fieldPath": "sourceType", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "analysisJobs",
      "queryScope": "COLLECTION", 
      "fields": [
        { "fieldPath": "clerkUserId", "order": "ASCENDING" },
        { "fieldPath": "status", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "analysisJobs",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "clerkUserId", "order": "ASCENDING" },
        { "fieldPath": "sourceType", "order": "ASCENDING" },
        { "fieldPath": "status", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    }
  ]
}
```

### Index Deployment Command
```bash
# Deploy new indexes
firebase deploy --only firestore:indexes

# Verify indexes are built
firebase firestore:indexes
```

## Query Patterns for New Page

### 1. List All Jobs for User (Paginated)
```typescript
const query = database
  .collection('analysisJobs')
  .where('clerkUserId', '==', userId)
  .orderBy('createdAt', 'desc')
  .limit(pageSize);

// For pagination
const nextQuery = query.startAfter(lastDoc);
```

### 2. Filter by Source Type
```typescript
const query = database
  .collection('analysisJobs')
  .where('clerkUserId', '==', userId)
  .where('sourceType', '==', 'manual')
  .orderBy('createdAt', 'desc');
```

### 3. Filter by Status
```typescript
const query = database
  .collection('analysisJobs')
  .where('clerkUserId', '==', userId)
  .where('status', '==', 'processing')
  .orderBy('createdAt', 'desc');
```

### 4. Combined Filters
```typescript
const query = database
  .collection('analysisJobs')
  .where('clerkUserId', '==', userId)
  .where('sourceType', '==', 'manual')
  .where('status', '==', 'completed')
  .orderBy('createdAt', 'desc');
```

## Security Rules Updates

### Updated Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Analysis jobs restricted to creator
    match /analysisJobs/{jobId} {
      allow read, write: if request.auth.uid == resource.data.clerkUserId;
      
      // Allow creation with proper user ID
      allow create: if request.auth.uid == request.resource.data.clerkUserId
        && request.resource.data.sourceType in ['manual', 'automatic'];
    }
  }
}
```

## Data Validation

### TypeScript Interfaces for API
```typescript
// Request interfaces
interface ListAnalysisRunsRequest {
  page?: number;
  limit?: number;
  sourceType?: 'manual' | 'automatic';
  status?: 'processing' | 'completed' | 'error' | 'cancelled';
  startDate?: string;  // Filter by analysis run creation date
  endDate?: string;
}

// Response interfaces
interface AnalysisRunSummary {
  jobId: string;
  sourceType: 'manual' | 'automatic';
  status: 'processing' | 'completed' | 'error' | 'cancelled';
  totalEmails: number;           // total field: emails found in date range
  cachedEmails: number;          // cachedCount field: emails with existing analysis
  pendingEmails: number;         // calculated: total - processed
  successfullyAnalyzed: number;  // processed field: completed emails
  emailDateRange: {              // startDate/endDate: email date range analyzed
    start: string;
    end: string;
  };
  analysisStartTime: string;     // createdAt: when analysis run started
  lastUpdated: string;           // updatedAt: last progress update
  completedAt?: string;          // completedAt: when analysis finished
  error?: string;                // error: failure message if any
}

interface ListAnalysisRunsResponse {
  analysisRuns: AnalysisRunSummary[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRuns: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
```

## Performance Considerations

### Optimization Strategies
1. **Pagination**: Use cursor-based pagination for better performance
2. **Caching**: Cache frequently accessed job lists
3. **Indexes**: Ensure all query patterns have supporting indexes
4. **Batch Operations**: Use batch writes for bulk updates

### Monitoring Queries
```typescript
// Monitor query performance
const query = database
  .collection('analysisJobs')
  .where('clerkUserId', '==', userId)
  .orderBy('createdAt', 'desc')
  .limit(20);

console.time('analysisJobs-query');
const snapshot = await query.get();
console.timeEnd('analysisJobs-query');
```

## Rollback Plan

### Emergency Rollback Strategy
1. **Remove new fields**: Not required as they're additive
2. **Revert API changes**: Deploy previous version of API endpoints
3. **Index cleanup**: Remove new indexes if causing issues
4. **Data integrity**: New fields don't affect existing functionality

### Rollback Commands
```bash
# Revert to previous deployment
gcloud run deploy webapp --image=gcr.io/PROJECT/webapp:previous-tag

# Remove new indexes (if needed)
firebase firestore:indexes --delete
```

---

*This schema update maintains full backward compatibility while enabling the new Email Analysis Status page functionality.*
