# 🧪 Testing Strategy for Email Analysis Status Page

## Overview

This document outlines the comprehensive testing strategy for the Email Analysis Status page, including unit tests, integration tests, end-to-end tests, and performance testing.

## Testing Pyramid

```mermaid
graph TB
    subgraph "Testing Pyramid"
        E2E[End-to-End Tests<br/>- User workflows<br/>- Cross-browser testing<br/>- Real-time updates]
        Integration[Integration Tests<br/>- API endpoints<br/>- Database queries<br/>- Component integration]
        Unit[Unit Tests<br/>- Component logic<br/>- Utility functions<br/>- State management]
    end
    
    Unit --> Integration
    Integration --> E2E
    
    style Unit fill:#e1f5fe
    style Integration fill:#f3e5f5
    style E2E fill:#fff3e0
```

## Unit Testing

### 1. Component Tests

#### AnalysisRunsTable Component
```typescript
// apps/webapp/src/app/analysis/components/__tests__/AnalysisRunsTable.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AnalysisRunsTable } from '../AnalysisRunsTable';
import { mockAnalysisRuns } from '../../__mocks__/analysisRuns';

describe('AnalysisRunsTable', () => {
  const defaultProps = {
    analysisRuns: mockAnalysisRuns,
    loading: false,
    pagination: {
      currentPage: 1,
      totalPages: 5,
      totalRuns: 87,
      hasNextPage: true,
      hasPreviousPage: false,
      pageSize: 20
    },
    filters: {},
    onFilterChange: jest.fn(),
    onPageChange: jest.fn(),
    onSortChange: jest.fn(),
    onRunSelect: jest.fn(),
    onRunAction: jest.fn()
  };

  test('renders analysis runs table with correct data', () => {
    render(<AnalysisRunsTable {...defaultProps} />);

    expect(screen.getByText('Source Type')).toBeInTheDocument();
    expect(screen.getByText('Total Emails')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();

    // Check first analysis run data
    expect(screen.getByText('Manual')).toBeInTheDocument();
    expect(screen.getByText('150')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
  });

  test('handles sorting correctly', async () => {
    const onSortChange = jest.fn();
    render(<AnalysisJobsTable {...defaultProps} onSortChange={onSortChange} />);
    
    fireEvent.click(screen.getByText('Total Emails'));
    
    await waitFor(() => {
      expect(onSortChange).toHaveBeenCalledWith('totalEmails', 'asc');
    });
  });

  test('handles pagination correctly', async () => {
    const onPageChange = jest.fn();
    render(<AnalysisJobsTable {...defaultProps} onPageChange={onPageChange} />);
    
    fireEvent.click(screen.getByText('Next'));
    
    await waitFor(() => {
      expect(onPageChange).toHaveBeenCalledWith(2);
    });
  });

  test('displays loading state correctly', () => {
    render(<AnalysisJobsTable {...defaultProps} loading={true} />);
    
    expect(screen.getByTestId('table-loading')).toBeInTheDocument();
    expect(screen.getAllByTestId('skeleton-row')).toHaveLength(5);
  });

  test('displays empty state when no analysis runs', () => {
    render(<AnalysisRunsTable {...defaultProps} analysisRuns={[]} />);

    expect(screen.getByText('No analysis runs found')).toBeInTheDocument();
    expect(screen.getByText('Start your first email analysis')).toBeInTheDocument();
  });
});
```

#### StatusBadge Component
```typescript
// apps/webapp/src/app/components/ui/__tests__/StatusBadge.test.tsx
import { render, screen } from '@testing-library/react';
import { StatusBadge } from '../StatusBadge';

describe('StatusBadge', () => {
  test('renders processing status correctly', () => {
    render(<StatusBadge status="processing" />);
    
    expect(screen.getByText('Processing')).toBeInTheDocument();
    expect(screen.getByTestId('status-badge')).toHaveClass('bg-blue-100', 'text-blue-800');
  });

  test('renders completed status correctly', () => {
    render(<StatusBadge status="completed" />);
    
    expect(screen.getByText('Completed')).toBeInTheDocument();
    expect(screen.getByTestId('status-badge')).toHaveClass('bg-green-100', 'text-green-800');
  });

  test('renders error status correctly', () => {
    render(<StatusBadge status="error" />);
    
    expect(screen.getByText('Error')).toBeInTheDocument();
    expect(screen.getByTestId('status-badge')).toHaveClass('bg-red-100', 'text-red-800');
  });

  test('shows animated indicator for processing status', () => {
    render(<StatusBadge status="processing" animated={true} />);
    
    expect(screen.getByTestId('animated-indicator')).toBeInTheDocument();
  });
});
```

### 2. Hook Tests

#### useAnalysisJobs Hook
```typescript
// apps/webapp/src/app/analysis/hooks/__tests__/useAnalysisJobs.test.ts
import { renderHook, act, waitFor } from '@testing-library/react';
import { useAnalysisJobs } from '../useAnalysisJobs';
import * as api from '../../services/analysisJobsService';

jest.mock('../../services/analysisJobsService');
const mockApi = api as jest.Mocked<typeof api>;

describe('useAnalysisJobs', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('fetches jobs on mount', async () => {
    const mockJobs = [{ jobId: '1', status: 'completed' }];
    mockApi.getJobs.mockResolvedValue({
      jobs: mockJobs,
      pagination: { currentPage: 1, totalPages: 1, totalJobs: 1 }
    });

    const { result } = renderHook(() => useAnalysisJobs());

    await waitFor(() => {
      expect(result.current.jobs).toEqual(mockJobs);
      expect(result.current.loading).toBe(false);
    });
  });

  test('handles API errors gracefully', async () => {
    mockApi.getJobs.mockRejectedValue(new Error('API Error'));

    const { result } = renderHook(() => useAnalysisJobs());

    await waitFor(() => {
      expect(result.current.error).toBe('Failed to fetch analysis jobs');
      expect(result.current.loading).toBe(false);
    });
  });

  test('updates filters correctly', async () => {
    const { result } = renderHook(() => useAnalysisJobs());

    act(() => {
      result.current.actions.updateFilters({ sourceType: 'manual' });
    });

    expect(result.current.filters.sourceType).toBe('manual');
  });
});
```

## Integration Testing

### 1. API Endpoint Tests

#### Analysis Jobs API
```typescript
// apps/webapp/src/app/api/analysis-jobs/__tests__/route.test.ts
import { GET } from '../route';
import { NextRequest } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { Database } from '@webapp/services';

jest.mock('@clerk/nextjs/server');
jest.mock('@webapp/services');

const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockDatabase = Database as jest.MockedClass<typeof Database>;

describe('/api/analysis-jobs', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('returns paginated jobs for authenticated user', async () => {
    mockAuth.mockResolvedValue({ userId: 'user123' });
    
    const mockJobs = [
      { jobId: '1', clerkUserId: 'user123', status: 'completed' },
      { jobId: '2', clerkUserId: 'user123', status: 'processing' }
    ];
    
    mockDatabase.prototype.collection.mockReturnValue({
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      get: jest.fn().mockResolvedValue({
        docs: mockJobs.map(job => ({ data: () => job }))
      })
    });

    const request = new NextRequest('http://localhost/api/analysis-jobs?page=1&limit=10');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.jobs).toHaveLength(2);
    expect(data.pagination.currentPage).toBe(1);
  });

  test('filters jobs by source type', async () => {
    mockAuth.mockResolvedValue({ userId: 'user123' });
    
    const request = new NextRequest('http://localhost/api/analysis-jobs?sourceType=manual');
    const response = await GET(request);

    expect(response.status).toBe(200);
    // Verify that the database query included the sourceType filter
  });

  test('returns 401 for unauthenticated requests', async () => {
    mockAuth.mockResolvedValue({ userId: null });

    const request = new NextRequest('http://localhost/api/analysis-jobs');
    const response = await GET(request);

    expect(response.status).toBe(401);
  });
});
```

### 2. Database Integration Tests

```typescript
// tests/integration/database.test.ts
import { Database } from '@webapp/services';
import { initializeTestEnvironment } from '@firebase/rules-unit-testing';

describe('Database Integration', () => {
  let testEnv: any;
  let database: Database;

  beforeAll(async () => {
    testEnv = await initializeTestEnvironment({
      projectId: 'test-project',
      firestore: {
        rules: `
          rules_version = '2';
          service cloud.firestore {
            match /databases/{database}/documents {
              match /analysisJobs/{jobId} {
                allow read, write: if request.auth.uid == resource.data.clerkUserId;
              }
            }
          }
        `
      }
    });
    
    database = new Database();
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  test('queries analysis jobs with proper filtering', async () => {
    // Create test data
    const testJob = {
      jobId: 'test-job-1',
      clerkUserId: 'user123',
      sourceType: 'manual',
      status: 'completed',
      createdAt: new Date()
    };

    await database.collection('analysisJobs').doc('test-job-1').set(testJob);

    // Query with filters
    const query = database
      .collection('analysisJobs')
      .where('clerkUserId', '==', 'user123')
      .where('sourceType', '==', 'manual');

    const snapshot = await query.get();
    
    expect(snapshot.docs).toHaveLength(1);
    expect(snapshot.docs[0].data().jobId).toBe('test-job-1');
  });
});
```

## End-to-End Testing

### 1. User Workflow Tests

```typescript
// tests/e2e/analysis-page.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Email Analysis Status Page', () => {
  test.beforeEach(async ({ page }) => {
    // Login and navigate to analysis page
    await page.goto('/sign-in');
    await page.fill('[name="identifier"]', '<EMAIL>');
    await page.fill('[name="password"]', 'testpassword');
    await page.click('button[type="submit"]');
    await page.waitForURL('/');
    await page.goto('/analysis');
  });

  test('displays analysis jobs table', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Email Analysis Status');
    await expect(page.locator('[data-testid="analysis-jobs-table"]')).toBeVisible();
    
    // Check table headers
    await expect(page.locator('th')).toContainText(['Source Type', 'Total Emails', 'Status']);
  });

  test('starts new email analysis', async ({ page }) => {
    // Fill in date range
    await page.fill('[data-testid="start-date"]', '2024-01-01');
    await page.fill('[data-testid="end-date"]', '2024-01-31');
    
    // Click analyze button
    await page.click('[data-testid="analyze-button"]');
    
    // Verify analysis started
    await expect(page.locator('[data-testid="progress-indicator"]')).toBeVisible();
    await expect(page.locator('text=Starting email analysis')).toBeVisible();
  });

  test('filters jobs by source type', async ({ page }) => {
    // Open filter dropdown
    await page.click('[data-testid="source-type-filter"]');
    await page.click('text=Manual');
    
    // Verify filter applied
    await expect(page.locator('[data-testid="filter-badge"]')).toContainText('Manual');
    
    // Check that only manual jobs are shown
    const sourceTypeCells = page.locator('[data-testid="source-type-cell"]');
    const count = await sourceTypeCells.count();
    
    for (let i = 0; i < count; i++) {
      await expect(sourceTypeCells.nth(i)).toContainText('Manual');
    }
  });

  test('sorts jobs by total emails', async ({ page }) => {
    // Click on Total Emails header to sort
    await page.click('[data-testid="sort-total-emails"]');
    
    // Verify sort indicator
    await expect(page.locator('[data-testid="sort-indicator-asc"]')).toBeVisible();
    
    // Verify jobs are sorted (check first few rows)
    const emailCounts = await page.locator('[data-testid="total-emails-cell"]').allTextContents();
    const numbers = emailCounts.map(text => parseInt(text));
    
    // Check if sorted in ascending order
    for (let i = 1; i < numbers.length; i++) {
      expect(numbers[i]).toBeGreaterThanOrEqual(numbers[i - 1]);
    }
  });

  test('navigates through pagination', async ({ page }) => {
    // Verify pagination controls
    await expect(page.locator('[data-testid="pagination"]')).toBeVisible();
    
    // Click next page
    await page.click('[data-testid="next-page"]');
    
    // Verify page changed
    await expect(page.locator('[data-testid="current-page"]')).toContainText('2');
    
    // Click previous page
    await page.click('[data-testid="previous-page"]');
    
    // Verify back to page 1
    await expect(page.locator('[data-testid="current-page"]')).toContainText('1');
  });

  test('shows real-time updates for processing jobs', async ({ page }) => {
    // Start a new analysis to create a processing job
    await page.fill('[data-testid="start-date"]', '2024-01-01');
    await page.fill('[data-testid="end-date"]', '2024-01-02');
    await page.click('[data-testid="analyze-button"]');
    
    // Wait for job to appear in table
    await expect(page.locator('[data-testid="status-processing"]')).toBeVisible();
    
    // Wait for progress updates (polling should update the UI)
    await page.waitForTimeout(6000); // Wait for at least one polling cycle
    
    // Verify progress indicator updates
    const progressText = await page.locator('[data-testid="progress-text"]').textContent();
    expect(progressText).toMatch(/\d+ of \d+ emails processed/);
  });
});
```

### 2. Mobile Responsiveness Tests

```typescript
// tests/e2e/mobile-analysis-page.spec.ts
import { test, expect, devices } from '@playwright/test';

test.use({ ...devices['iPhone 12'] });

test.describe('Mobile Analysis Page', () => {
  test('displays mobile-optimized layout', async ({ page }) => {
    await page.goto('/analysis');
    
    // Verify mobile layout
    await expect(page.locator('[data-testid="mobile-job-cards"]')).toBeVisible();
    await expect(page.locator('[data-testid="desktop-table"]')).not.toBeVisible();
  });

  test('mobile job cards show essential information', async ({ page }) => {
    await page.goto('/analysis');
    
    const jobCard = page.locator('[data-testid="job-card"]').first();
    
    await expect(jobCard.locator('[data-testid="job-status"]')).toBeVisible();
    await expect(jobCard.locator('[data-testid="job-progress"]')).toBeVisible();
    await expect(jobCard.locator('[data-testid="job-date-range"]')).toBeVisible();
  });
});
```

## Performance Testing

### 1. Load Testing

```typescript
// tests/performance/analysis-page-load.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Analysis Page Performance', () => {
  test('page loads within performance budget', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/analysis');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Page should load within 2 seconds
    expect(loadTime).toBeLessThan(2000);
  });

  test('table renders large dataset efficiently', async ({ page }) => {
    // Mock API to return large dataset
    await page.route('/api/analysis-jobs*', async route => {
      const jobs = Array.from({ length: 100 }, (_, i) => ({
        jobId: `job-${i}`,
        sourceType: i % 2 === 0 ? 'manual' : 'automatic',
        status: 'completed',
        totalEmails: Math.floor(Math.random() * 1000),
        // ... other fields
      }));
      
      await route.fulfill({
        json: { jobs, pagination: { currentPage: 1, totalPages: 5, totalJobs: 500 } }
      });
    });
    
    const startTime = Date.now();
    await page.goto('/analysis');
    await page.waitForSelector('[data-testid="analysis-jobs-table"]');
    
    const renderTime = Date.now() - startTime;
    
    // Table should render within 500ms
    expect(renderTime).toBeLessThan(500);
  });
});
```

## Test Data Management

### 1. Mock Data Factory

```typescript
// apps/webapp/src/app/analysis/__mocks__/analysisJobs.ts
export const createMockAnalysisJob = (overrides = {}) => ({
  jobId: 'mock-job-1',
  sourceType: 'manual' as const,
  status: 'completed' as const,
  totalEmails: 150,
  cachedEmails: 45,
  pendingEmails: 0,
  successfullyAnalyzed: 150,
  dateRange: {
    start: '2024-01-01',
    end: '2024-01-31'
  },
  jobStartTime: '2024-01-15T10:30:00Z',
  lastUpdated: '2024-01-15T11:45:00Z',
  completedAt: '2024-01-15T11:45:00Z',
  currentStatus: 'Analysis completed successfully',
  ...overrides
});

export const mockAnalysisJobs = [
  createMockAnalysisJob(),
  createMockAnalysisJob({
    jobId: 'mock-job-2',
    sourceType: 'automatic',
    status: 'processing',
    totalEmails: 75,
    pendingEmails: 25,
    successfullyAnalyzed: 50,
    completedAt: undefined
  }),
  createMockAnalysisJob({
    jobId: 'mock-job-3',
    status: 'error',
    error: 'Insufficient tokens'
  })
];
```

## Continuous Integration

### 1. Test Pipeline Configuration

```yaml
# .github/workflows/test-analysis-page.yml
name: Analysis Page Tests

on:
  pull_request:
    paths:
      - 'apps/webapp/src/app/analysis/**'
      - 'apps/webapp/src/app/api/analysis-jobs/**'

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm ci
      - run: npm run test:unit -- --testPathPattern=analysis
      
  integration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm ci
      - run: npm run test:integration -- --testPathPattern=analysis
      
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e -- tests/e2e/analysis-page.spec.ts
```

---

*This comprehensive testing strategy ensures the Email Analysis Status page is robust, performant, and user-friendly.*
